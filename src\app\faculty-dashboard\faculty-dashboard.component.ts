import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { BaseDashboardComponent } from '../shared/base-dashboard.component';
import { FacultyAuthService } from '../services/faculty-auth.service';

@Component({
  selector: 'app-faculty-dashboard',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="min-h-screen transition-colors duration-300 flex" [class]="isDarkMode ? 'bg-gray-900' : 'bg-gray-50'">
      <div class="flex-1 p-6">
        <h1 class="text-2xl font-bold mb-4" [class]="isDarkMode ? 'text-white' : 'text-gray-900'">
          Faculty Dashboard - Coming Soon
        </h1>
        <p [class]="isDarkMode ? 'text-gray-300' : 'text-gray-600'">
          Faculty dashboard features will be implemented next...
        </p>
        <button 
          (click)="showLogout()"
          class="mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200"
        >
          Logout
        </button>
      </div>
    </div>

    <!-- Logout Confirmation Modal -->
    <div *ngIf="showLogoutModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="rounded-lg p-6 max-w-sm w-full mx-4 transition-colors duration-300" [class]="isDarkMode ? 'bg-gray-800' : 'bg-white'">
        <h3 class="text-lg font-medium mb-4" [class]="isDarkMode ? 'text-white' : 'text-gray-900'">Confirm Logout</h3>
        <p class="text-sm mb-6" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-600'">Are you sure you want to log out?</p>
        <div class="flex space-x-3">
          <button (click)="cancelLogout()" class="flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200" [class]="isDarkMode ? 'bg-gray-700 text-white hover:bg-gray-600' : 'bg-gray-200 text-gray-900 hover:bg-gray-300'">
            Cancel
          </button>
          <button (click)="confirmLogout()" class="flex-1 px-4 py-2 text-sm font-medium rounded-md bg-red-600 text-white hover:bg-red-700 transition-colors duration-200">
            Logout
          </button>
        </div>
      </div>
    </div>
  `,
  styles: []
})
export class FacultyDashboardComponent extends BaseDashboardComponent implements OnInit, OnDestroy {

  constructor(
    protected override router: Router,
    private facultyAuthService: FacultyAuthService
  ) {
    super(router, facultyAuthService as any);
  }

  override ngOnInit(): void {
    super.ngOnInit();
  }

  override ngOnDestroy(): void {
    // Clean up subscriptions if any
  }

  protected initializeDashboard(): void {
    const savedMode = localStorage.getItem('darkMode');
    this.isDarkMode = savedMode === 'true';
  }

  protected getLogoutRedirectRoute(): string {
    return '/facultylogin';
  }

  protected getAIResponses(userMessage: string): string[] {
    return [
      "I'm here to help faculty with library resources and course materials!",
      "How can I assist you with your teaching and research needs?",
      "I can help you find academic resources, manage course materials, and more."
    ];
  }

  onNavigate(section: string): void {
    console.log(`Faculty navigating to ${section}`);
  }

  onNotificationClick(): void {
    console.log('Faculty notifications clicked');
  }

  onProfileClick(): void {
    console.log('Faculty profile clicked');
  }
}
