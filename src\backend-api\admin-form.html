<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Registration Form</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .server-status {
            text-align: center;
            margin-bottom: 20px;
            padding: 10px;
            border-radius: 5px;
        }
        .online {
            background-color: #d4edda;
            color: #155724;
        }
        .offline {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Admin Registration</h1>
        
        <div id="serverStatus" class="server-status">
            Checking server status...
        </div>

        <form id="adminForm">
            <div class="form-group">
                <label for="fullName">Full Name:</label>
                <input type="text" id="fullName" name="fullName" required>
            </div>

            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" name="email" required>
            </div>

            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" required>
                <small>Must be at least 6 characters with uppercase, lowercase, and number</small>
            </div>

            <div class="form-group">
                <label for="role">Role:</label>
                <select id="role" name="role" required>
                    <option value="">Select Role</option>
                    <option value="Super Admin">Super Admin</option>
                    <option value="Data Center Admin">Data Center Admin</option>
                    <option value="Librarian">Librarian</option>
                    <option value="Librarian Staff">Librarian Staff</option>
                </select>
            </div>

            <div class="form-group">
                <label for="status">Status:</label>
                <select id="status" name="status" required>
                    <option value="Active">Active</option>
                    <option value="Inactive">Inactive</option>
                </select>
            </div>

            <button type="submit">Register Admin</button>
        </form>

        <div id="result" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000/api/v1/adminauth';
        
        // Check server status
        async function checkServerStatus() {
            const statusDiv = document.getElementById('serverStatus');
            try {
                const response = await fetch('http://localhost:3000/');
                if (response.ok) {
                    statusDiv.textContent = '✅ Server is online and ready';
                    statusDiv.className = 'server-status online';
                } else {
                    throw new Error('Server responded with error');
                }
            } catch (error) {
                statusDiv.textContent = '❌ Server is offline. Please start with: npm start';
                statusDiv.className = 'server-status offline';
            }
        }

        // Handle form submission
        document.getElementById('adminForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const adminData = {
                fullName: formData.get('fullName'),
                email: formData.get('email'),
                password: formData.get('password'),
                role: formData.get('role'),
                status: formData.get('status')
            };

            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = 'Creating admin...';
            resultDiv.className = 'result';

            try {
                const response = await fetch(`${API_BASE}/register-admin`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(adminData)
                });

                const result = await response.json();

                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h3>✅ Admin Created Successfully!</h3>
                        <p><strong>Admin ID:</strong> ${result.data.adminID}</p>
                        <p><strong>Name:</strong> ${result.data.fullName}</p>
                        <p><strong>Email:</strong> ${result.data.email}</p>
                        <p><strong>Role:</strong> ${result.data.role}</p>
                        <p><strong>Status:</strong> ${result.data.status}</p>
                    `;
                    document.getElementById('adminForm').reset();
                } else {
                    throw new Error(result.error || 'Unknown error');
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h3>❌ Error Creating Admin</h3>
                    <p>${error.message}</p>
                `;
            }
        });

        // Check server status on page load
        checkServerStatus();
    </script>
</body>
</html>
