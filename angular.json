{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"Library-Management-System-AI": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular/build:application", "options": {"browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": [{"glob": "**/*", "input": "public"}, {"glob": "**/*", "input": "src/assets", "output": "assets"}], "styles": ["src/styles.css"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "1MB", "maximumError": "2MB"}, {"type": "anyComponentStyle", "maximumWarning": "10kB", "maximumError": "15kB"}], "outputHashing": "all", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}]}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "budgets": []}, "separated": {"optimization": false, "extractLicenses": false, "sourceMap": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.separated.ts"}]}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular/build:dev-server", "configurations": {"production": {"buildTarget": "Library-Management-System-AI:build:production"}, "development": {"buildTarget": "Library-Management-System-AI:build:development"}, "separated": {"buildTarget": "Library-Management-System-AI:build:separated"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular/build:extract-i18n"}, "test": {"builder": "@angular/build:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "assets": [{"glob": "**/*", "input": "public"}, {"glob": "**/*", "input": "src/assets", "output": "assets"}], "styles": ["src/styles.css"]}}}}}}