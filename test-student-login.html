<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Student Login</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Test Student Login</h1>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="studentId">Student ID (YYYY-NNNNN format):</label>
                <input type="text" id="studentId" placeholder="2000-00001" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" placeholder="Enter password" required>
            </div>
            
            <button type="submit">🚀 Test Login</button>
            <button type="button" onclick="testSessionValidation()">🔍 Test Session Validation</button>
        </form>
        
        <div id="result"></div>
    </div>

    <script>
        let currentToken = null;

        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const studentId = document.getElementById('studentId').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            resultDiv.innerHTML = '<div class="result info">🔄 Testing student login...</div>';
            
            try {
                console.log('🚀 Testing student login...');
                
                const response = await fetch('http://localhost:3000/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        studentId: studentId,
                        password: password
                    })
                });

                const data = await response.json();
                console.log('📋 Response:', data);

                if (data.success) {
                    currentToken = data.token;
                    resultDiv.innerHTML = `
                        <div class="result success">
                            ✅ Login successful!<br>
                            Student: ${data.data.FullName}<br>
                            Course: ${data.data.Course}<br>
                            Token received: ${data.token ? 'Yes' : 'No'}
                        </div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            ❌ Login failed: ${data.error}
                        </div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                console.error('❌ Error:', error);
                resultDiv.innerHTML = `
                    <div class="result error">
                        ❌ Network error: ${error.message}
                    </div>
                `;
            }
        });

        async function testSessionValidation() {
            const resultDiv = document.getElementById('result');
            
            if (!currentToken) {
                resultDiv.innerHTML = '<div class="result error">❌ No token available. Please login first.</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="result info">🔄 Testing session validation...</div>';
            
            try {
                console.log('🔍 Testing session validation...');
                
                const response = await fetch('http://localhost:3000/api/v1/auth/validate-session', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        token: currentToken
                    })
                });

                const data = await response.json();
                console.log('📋 Validation Response:', data);

                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            ✅ Session validation successful!<br>
                            Student: ${data.data.FullName}<br>
                            Status: ${data.data.AccountStatus}
                        </div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            ❌ Session validation failed: ${data.error}
                        </div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                console.error('❌ Error:', error);
                resultDiv.innerHTML = `
                    <div class="result error">
                        ❌ Network error: ${error.message}
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
