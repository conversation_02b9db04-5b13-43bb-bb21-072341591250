// Simple test to verify frontend-backend communication
const http = require('http');

function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const request = http.get(url, (response) => {
      let data = '';
      response.on('data', (chunk) => {
        data += chunk;
      });
      response.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ data: jsonData, headers: response.headers, status: response.statusCode });
        } catch (error) {
          resolve({ data: data, headers: response.headers, status: response.statusCode });
        }
      });
    });

    request.on('error', (error) => {
      reject(error);
    });

    request.setTimeout(5000, () => {
      request.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

async function testBackendConnection() {
  console.log('🧪 Testing Backend Connection...\n');

  try {
    // Test 1: Health Check
    console.log('1️⃣ Testing Health Check...');
    const healthResponse = await makeRequest('http://localhost:3000/');
    console.log('✅ Health Check Success:', healthResponse.data.message);
    console.log('   Status:', healthResponse.data.status);
    console.log('   Version:', healthResponse.data.version);
    console.log('');

    // Test 2: Weather API
    console.log('2️⃣ Testing Weather API...');
    const weatherResponse = await makeRequest('http://localhost:3000/api/v1/weather');
    console.log('✅ Weather API Success!');
    console.log('   Location:', weatherResponse.data.data.location);
    console.log('   Temperature:', weatherResponse.data.data.temperature + '°C');
    console.log('   Condition:', weatherResponse.data.data.description);
    console.log('   Humidity:', weatherResponse.data.data.humidity + '%');
    console.log('');

    // Test 3: CORS Headers
    console.log('3️⃣ Testing CORS Headers...');
    console.log('✅ CORS Headers Present:');
    console.log('   Access-Control-Allow-Origin:', healthResponse.headers['access-control-allow-origin'] || 'Not set');
    console.log('   Access-Control-Allow-Methods:', healthResponse.headers['access-control-allow-methods'] || 'Not set');
    console.log('');

    // Test 4: API Endpoints
    console.log('4️⃣ Testing API Info Endpoint...');
    const apiResponse = await makeRequest('http://localhost:3000/api');
    console.log('✅ API Info Success!');
    console.log('   Available Endpoints:');
    Object.entries(apiResponse.data.endpoints).forEach(([key, value]) => {
      console.log(`     ${key}: ${value}`);
    });
    console.log('');

    console.log('🎉 All Backend Tests Passed!');
    console.log('🔗 Backend is ready for frontend communication');

  } catch (error) {
    console.error('❌ Backend Test Failed:');
    console.error('   Error:', error.message);
    console.log('');
    console.log('💡 Make sure backend is running: cd src/backend-api && npm start');
  }
}

// Test environment configuration simulation
function testEnvironmentConfig() {
  console.log('\n🌍 Testing Environment Configuration...\n');
  
  const environments = {
    development: {
      apiUrl: 'http://localhost:3000/api/v1',
      backendUrl: 'http://localhost:3000'
    },
    separated: {
      apiUrl: 'http://localhost:3000/api/v1',
      backendUrl: 'http://localhost:3000'
    },
    production: {
      apiUrl: 'https://your-backend-domain.com/api/v1',
      backendUrl: 'https://your-backend-domain.com'
    }
  };
  
  Object.entries(environments).forEach(([env, config]) => {
    console.log(`📋 ${env.toUpperCase()} Environment:`);
    console.log(`   API URL: ${config.apiUrl}`);
    console.log(`   Backend URL: ${config.backendUrl}`);
    console.log('');
  });
  
  console.log('✅ Environment configurations are properly set up');
}

// Run tests
async function runAllTests() {
  console.log('🚀 Starting Frontend-Backend Communication Tests\n');
  console.log('=' .repeat(60));
  
  await testBackendConnection();
  testEnvironmentConfig();
  
  console.log('=' .repeat(60));
  console.log('✅ Testing Complete!');
  console.log('');
  console.log('📝 Next Steps:');
  console.log('   1. Start frontend: npm start');
  console.log('   2. Visit: http://localhost:4200');
  console.log('   3. Check browser console for API calls');
  console.log('   4. Test separated mode: npm run start:separated');
}

runAllTests();
