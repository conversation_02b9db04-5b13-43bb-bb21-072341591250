// Test script to create a faculty account and test login
const bcrypt = require('bcryptjs');
const mysql = require('mysql2/promise');

async function createTestFaculty() {
  try {
    // Database connection
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'library_management_system'
    });

    console.log('📊 Connected to database');

    // Hash password
    const password = 'FacultyPass123';
    const hashedPassword = await bcrypt.hash(password, 10);

    // Faculty data
    const facultyData = {
      facultyId: **********, // Numeric format for database
      fullName: 'Dr. <PERSON>',
      email: '<EMAIL>',
      phoneNumber: '***********',
      password: hashedPassword,
      department: 'Computer Science',
      position: 'Professor',
      status: 'Active'
    };

    // Check if faculty already exists
    const [existing] = await connection.execute(
      'SELECT FacultyID FROM faculty WHERE FacultyID = ?',
      [facultyData.facultyId]
    );

    if (existing.length > 0) {
      console.log('✅ Test faculty already exists');
    } else {
      // Insert test faculty
      await connection.execute(
        `INSERT INTO faculty (FacultyID, FullName, Email, PhoneNumber, Password, Department, Position, Status) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          facultyData.facultyId,
          facultyData.fullName,
          facultyData.email,
          facultyData.phoneNumber,
          facultyData.password,
          facultyData.department,
          facultyData.position,
          facultyData.status
        ]
      );
      console.log('✅ Test faculty created successfully');
    }

    console.log('📋 Test Faculty Login Credentials:');
    console.log('Faculty ID: 2022-000001');
    console.log('Password: FacultyPass123');

    await connection.end();
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

createTestFaculty();
