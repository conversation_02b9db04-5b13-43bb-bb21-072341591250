// Test authentication flow with Node.js
const https = require('http');

console.log('🧪 Testing Student Authentication Flow');
console.log('=====================================');

// Test credentials
const testCredentials = {
  studentId: '2024-00001',
  password: 'password123'
};

console.log('📋 Test credentials:');
console.log(`   Student ID: ${testCredentials.studentId}`);
console.log(`   Password: ${testCredentials.password}`);
console.log('');

// Test login
function testLogin() {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(testCredentials);
    
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/v1/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    console.log('🚀 Testing login endpoint...');
    
    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          console.log(`📊 Response Status: ${res.statusCode}`);
          console.log('📋 Response Data:');
          console.log(JSON.stringify(response, null, 2));
          
          if (response.success && response.token) {
            console.log('✅ Login test PASSED');
            resolve(response.token);
          } else {
            console.log('❌ Login test FAILED');
            reject(new Error('Login failed'));
          }
        } catch (error) {
          console.log('❌ Error parsing response:', error.message);
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      console.log('❌ Request error:', error.message);
      reject(error);
    });

    req.write(postData);
    req.end();
  });
}

// Test session validation
function testSessionValidation(token) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify({ token });
    
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/v1/auth/validate-session',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    console.log('');
    console.log('🔍 Testing session validation endpoint...');
    
    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          console.log(`📊 Response Status: ${res.statusCode}`);
          console.log('📋 Response Data:');
          console.log(JSON.stringify(response, null, 2));
          
          if (response.success) {
            console.log('✅ Session validation test PASSED');
            resolve(response);
          } else {
            console.log('❌ Session validation test FAILED');
            reject(new Error('Session validation failed'));
          }
        } catch (error) {
          console.log('❌ Error parsing response:', error.message);
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      console.log('❌ Request error:', error.message);
      reject(error);
    });

    req.write(postData);
    req.end();
  });
}

// Run tests
async function runTests() {
  try {
    const token = await testLogin();
    await testSessionValidation(token);
    
    console.log('');
    console.log('🎉 All authentication tests PASSED!');
    console.log('✅ Backend authentication is working correctly');
    console.log('');
    console.log('🔧 Next steps:');
    console.log('1. Test the frontend login at http://localhost:4200/login');
    console.log('2. Use the test credentials above');
    console.log('3. Check browser console for any errors');
    console.log('4. Verify redirect to student dashboard works');
    
  } catch (error) {
    console.log('');
    console.log('❌ Authentication tests FAILED');
    console.log('Error:', error.message);
  }
}

runTests();
