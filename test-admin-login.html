<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Admin Login</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Test Admin Login</h1>
        <p>This tool tests the admin login API directly to debug authentication issues.</p>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" value="HelloNathan123" required>
            </div>
            
            <button type="submit">🚀 Test Login</button>
        </form>
        
        <div id="result"></div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            resultDiv.innerHTML = '<div class="result">🔄 Testing login...</div>';
            
            try {
                console.log('🚀 Testing admin login...');
                
                const response = await fetch('http://localhost:3000/api/v1/adminauth/login-admin', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        email: email,
                        password: password
                    })
                });
                
                console.log('📡 Response status:', response.status);
                
                const data = await response.json();
                console.log('📋 Response data:', data);
                
                if (response.ok && data.success) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            ✅ LOGIN SUCCESSFUL!
                            
                            Status: ${response.status}
                            Success: ${data.success}
                            Message: ${data.message}
                            
                            Admin Data:
                            - ID: ${data.data.AdminID}
                            - Name: ${data.data.FullName}
                            - Email: ${data.data.Email}
                            - Role: ${data.data.Role}
                            - Status: ${data.data.Status}
                            
                            Full Response:
                            ${JSON.stringify(data, null, 2)}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            ❌ LOGIN FAILED!
                            
                            Status: ${response.status}
                            Success: ${data.success || 'false'}
                            Error: ${data.error || data.message || 'Unknown error'}
                            
                            Full Response:
                            ${JSON.stringify(data, null, 2)}
                        </div>
                    `;
                }
                
            } catch (error) {
                console.error('❌ Login test error:', error);
                resultDiv.innerHTML = `
                    <div class="result error">
                        ❌ CONNECTION ERROR!
                        
                        Error: ${error.message}
                        
                        This usually means:
                        1. Backend server is not running
                        2. CORS issues
                        3. Network connectivity problems
                        
                        Make sure the backend server is running on http://localhost:3000
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
