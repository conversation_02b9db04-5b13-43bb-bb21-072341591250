// Test separated deployment configuration
const fs = require('fs');
const path = require('path');

function testEnvironmentFiles() {
  console.log('🧪 Testing Separated Deployment Configuration...\n');
  
  const envFiles = [
    'src/environments/environment.ts',
    'src/environments/environment.prod.ts', 
    'src/environments/environment.separated.ts'
  ];
  
  envFiles.forEach(file => {
    console.log(`📁 Checking ${file}...`);
    try {
      const content = fs.readFileSync(file, 'utf8');
      
      // Extract API URL from environment file
      const apiUrlMatch = content.match(/apiUrl:\s*['"`]([^'"`]+)['"`]/);
      const backendUrlMatch = content.match(/backendUrl:\s*['"`]([^'"`]+)['"`]/);
      
      if (apiUrlMatch && backendUrlMatch) {
        console.log(`   ✅ API URL: ${apiUrlMatch[1]}`);
        console.log(`   ✅ Backend URL: ${backendUrlMatch[1]}`);
      } else {
        console.log('   ❌ Could not parse URLs from environment file');
      }
      
    } catch (error) {
      console.log(`   ❌ Error reading file: ${error.message}`);
    }
    console.log('');
  });
}

function testAngularConfiguration() {
  console.log('🔧 Testing Angular Configuration...\n');
  
  try {
    const angularJson = JSON.parse(fs.readFileSync('angular.json', 'utf8'));
    const project = angularJson.projects['Library-Management-System-AI'];
    
    if (project && project.architect && project.architect.build && project.architect.build.configurations) {
      const configs = project.architect.build.configurations;
      
      console.log('📋 Available Build Configurations:');
      Object.keys(configs).forEach(config => {
        console.log(`   ✅ ${config}`);
        
        if (configs[config].fileReplacements) {
          configs[config].fileReplacements.forEach(replacement => {
            console.log(`      📄 ${replacement.replace} → ${replacement.with}`);
          });
        }
      });
      console.log('');
      
      // Check serve configurations
      if (project.architect.serve && project.architect.serve.configurations) {
        const serveConfigs = project.architect.serve.configurations;
        console.log('🚀 Available Serve Configurations:');
        Object.keys(serveConfigs).forEach(config => {
          console.log(`   ✅ ${config}: ${serveConfigs[config].buildTarget}`);
        });
        console.log('');
      }
      
    } else {
      console.log('❌ Could not find project configuration in angular.json');
    }
    
  } catch (error) {
    console.log(`❌ Error reading angular.json: ${error.message}`);
  }
}

function testBackendCorsConfiguration() {
  console.log('🌐 Testing Backend CORS Configuration...\n');
  
  try {
    const serverJs = fs.readFileSync('src/backend-api/server.js', 'utf8');
    
    // Extract CORS origins
    const corsMatch = serverJs.match(/origin:\s*[^}]+\[([\s\S]*?)\]/);
    if (corsMatch) {
      console.log('✅ CORS Origins configured:');
      const origins = corsMatch[1].match(/'([^']+)'/g);
      if (origins) {
        origins.forEach(origin => {
          console.log(`   🔗 ${origin.replace(/'/g, '')}`);
        });
      }
      console.log('');
    } else {
      console.log('❌ Could not find CORS configuration');
    }
    
    // Check for environment variable support
    if (serverJs.includes('process.env.ALLOWED_ORIGINS')) {
      console.log('✅ Environment variable support: ALLOWED_ORIGINS');
    } else {
      console.log('❌ No environment variable support found');
    }
    console.log('');
    
  } catch (error) {
    console.log(`❌ Error reading server.js: ${error.message}`);
  }
}

function testPackageJsonScripts() {
  console.log('📦 Testing Package.json Scripts...\n');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    
    const relevantScripts = [
      'start',
      'start:separated', 
      'build:separated',
      'backend:start',
      'dev:full',
      'dev:separated',
      'setup'
    ];
    
    console.log('🚀 Available Scripts:');
    relevantScripts.forEach(script => {
      if (packageJson.scripts[script]) {
        console.log(`   ✅ npm run ${script}: ${packageJson.scripts[script]}`);
      } else {
        console.log(`   ❌ npm run ${script}: Not found`);
      }
    });
    console.log('');
    
  } catch (error) {
    console.log(`❌ Error reading package.json: ${error.message}`);
  }
}

function testDeploymentFiles() {
  console.log('🚢 Testing Deployment Files...\n');
  
  const deploymentFiles = [
    'docker-compose.yml',
    'Dockerfile.frontend',
    'Dockerfile.backend',
    'nginx.conf',
    'DEPLOYMENT_GUIDE.md',
    'start-dev.bat',
    'start-separated.bat'
  ];
  
  deploymentFiles.forEach(file => {
    if (fs.existsSync(file)) {
      console.log(`   ✅ ${file} exists`);
    } else {
      console.log(`   ❌ ${file} missing`);
    }
  });
  console.log('');
}

function simulateSeparatedDeployment() {
  console.log('🔄 Simulating Separated Deployment Scenarios...\n');
  
  const scenarios = [
    {
      name: 'Same Machine, Different Ports',
      frontend: 'http://localhost:4200',
      backend: 'http://localhost:3000'
    },
    {
      name: 'Different Machines, Same Network',
      frontend: 'http://frontend-server:4200',
      backend: 'http://backend-server:3000'
    },
    {
      name: 'Cloud Deployment',
      frontend: 'https://your-frontend-domain.com',
      backend: 'https://your-backend-domain.com'
    },
    {
      name: 'Docker Containers',
      frontend: 'http://lms-frontend:80',
      backend: 'http://lms-backend:3000'
    }
  ];
  
  scenarios.forEach((scenario, index) => {
    console.log(`${index + 1}️⃣ ${scenario.name}:`);
    console.log(`   Frontend: ${scenario.frontend}`);
    console.log(`   Backend: ${scenario.backend}`);
    console.log(`   API Calls: ${scenario.frontend} → ${scenario.backend}/api/v1/*`);
    console.log('');
  });
}

// Run all tests
function runSeparatedDeploymentTests() {
  console.log('🚀 Testing Separated Deployment Configuration\n');
  console.log('=' .repeat(70));
  
  testEnvironmentFiles();
  testAngularConfiguration();
  testBackendCorsConfiguration();
  testPackageJsonScripts();
  testDeploymentFiles();
  simulateSeparatedDeployment();
  
  console.log('=' .repeat(70));
  console.log('✅ Separated Deployment Testing Complete!\n');
  
  console.log('📝 How to Test Separated Deployment:');
  console.log('');
  console.log('1️⃣ Same Machine Test:');
  console.log('   Terminal 1: cd src/backend-api && npm start');
  console.log('   Terminal 2: npm run start:separated');
  console.log('');
  console.log('2️⃣ Different Machines Test:');
  console.log('   Machine 1 (Backend): Copy src/backend-api/ and run npm start');
  console.log('   Machine 2 (Frontend): Update environment.separated.ts and run npm start');
  console.log('');
  console.log('3️⃣ Docker Test:');
  console.log('   docker-compose up --build');
  console.log('');
  console.log('4️⃣ Production Test:');
  console.log('   Update environment.prod.ts with production URLs');
  console.log('   npm run build');
  console.log('   Deploy dist/ folder to web server');
}

runSeparatedDeploymentTests();
