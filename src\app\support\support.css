/* Support Page Styles - Benedicto College Library Management System */

.support-container {
  background: white;
}

.support-card {
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.support-header {
  background: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 50%, #f59e0b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.support-divider {
  background: linear-gradient(90deg, #0ea5e9 0%, #3b82f6 25%, #f59e0b 75%, #f97316 100%);
  height: 4px;
  border-radius: 2px;
}

.help-category {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 2px solid transparent;
  background-clip: padding-box;
  transition: all 0.3s ease;
}

.help-category:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
}

.contact-card {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border-left: 5px solid #3b82f6;
}

.faq-item {
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
  border-left: 3px solid #f59e0b;
}

.support-icon {
  background: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 100%);
}

.category-icon {
  background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
}

.urgent-box {
  background: white;
  border: 2px solid #dc2626;
  border-radius: 8px;
}

.success-tip {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border-left: 5px solid #22c55e;
}

/* Search Input Enhancement */
#supportSearch:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Search Suggestions Dropdown */
#searchSuggestions {
  border-top: none;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.suggestion-item {
  transition: background-color 0.2s ease;
}

.suggestion-item:hover {
  background-color: #eff6ff !important;
}

/* Highlight effect para sa searched sections */
.highlight-section {
  background: linear-gradient(90deg, #fef3c7, #fde68a, #fef3c7);
  border-left-color: #f59e0b !important;
  border-left-width: 6px !important;
  animation: highlightPulse 3s ease-in-out;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.2);
}

@keyframes highlightPulse {
  0% {
    background: linear-gradient(90deg, #fef3c7, #fde68a, #fef3c7);
    transform: scale(1);
  }
  50% {
    background: linear-gradient(90deg, #fde68a, #fbbf24, #fde68a);
    transform: scale(1.02);
  }
  100% {
    background: linear-gradient(90deg, #fef3c7, #fde68a, #fef3c7);
    transform: scale(1);
  }
}

/* Popular search tags enhancement */
.popular-search-tag {
  transition: all 0.2s ease;
}

.popular-search-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .support-card {
    margin: 0.5rem;
    padding: 1.5rem;
  }

  .help-category {
    margin-bottom: 1rem;
  }

  .support-header {
    font-size: 2rem;
  }
}
