<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle with gradient -->
  <circle cx="32" cy="32" r="32" fill="url(#gradient0_linear_1_1)"/>
  
  <!-- Face -->
  <circle cx="32" cy="28" r="10" fill="white"/>
  
  <!-- Body -->
  <path d="M16 52C16 43.1634 23.1634 36 32 36C40.8366 36 48 43.1634 48 52V56H16V52Z" fill="white"/>
  
  <!-- Eyes -->
  <circle cx="26" cy="26" r="2" fill="#3B82F6"/>
  <circle cx="38" cy="26" r="2" fill="#3B82F6"/>
  
  <!-- Smile -->
  <path d="M28 32Q32 34 36 32" stroke="#3B82F6" stroke-width="2" stroke-linecap="round"/>
  
  <!-- AI Circuit pattern overlay -->
  <g opacity="0.3">
    <path d="M20 20L24 20L24 24" stroke="#1D4ED8" stroke-width="1" fill="none"/>
    <path d="M44 20L40 20L40 24" stroke="#1D4ED8" stroke-width="1" fill="none"/>
    <path d="M20 44L24 44L24 40" stroke="#1D4ED8" stroke-width="1" fill="none"/>
    <path d="M44 44L40 44L40 40" stroke="#1D4ED8" stroke-width="1" fill="none"/>
    <circle cx="22" cy="22" r="1" fill="#1D4ED8"/>
    <circle cx="42" cy="22" r="1" fill="#1D4ED8"/>
    <circle cx="22" cy="42" r="1" fill="#1D4ED8"/>
    <circle cx="42" cy="42" r="1" fill="#1D4ED8"/>
  </g>
  
  <!-- Gradient definition -->
  <defs>
    <linearGradient id="gradient0_linear_1_1" x1="0" y1="0" x2="64" y2="64" gradientUnits="userSpaceOnUse">
      <stop stop-color="#3B82F6"/>
      <stop offset="1" stop-color="#1D4ED8"/>
    </linearGradient>
  </defs>
</svg>
